# Product Requirements Document (PRD)
## Real-Time Roulette Tracking and Analytics Application

### Document Information
- **Version**: 1.0
- **Date**: 2025-01-22
- **Application Type**: Web-based Real-time Roulette Analytics Platform
- **Technology Stack**: Flask, Flask-SocketIO, Eventlet, HTML/CSS/JavaScript

---

## 1. Technical Architecture

### 1.1 System Overview
The application is a sophisticated real-time roulette tracking system that provides advanced analytics and betting recommendations through complex algorithms. It operates as a Flask web application with WebSocket support for real-time communication between administrators and users.

### 1.2 Core Technology Stack
- **Backend Framework**: Flask 2.0+
- **Real-time Communication**: Flask-SocketIO 5.0+ with Eventlet async mode
- **Web Server**: Eventlet WSGI server
- **Frontend**: Vanilla HTML5, CSS3, JavaScript with SocketIO client
- **Data Storage**: In-memory with file-based persistence
- **Security**: Werkzeug password hashing, session management, rate limiting
- **Logging**: Custom audit logging with rotating file handlers

### 1.3 Application Architecture
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Admin Client  │    │   User Clients   │    │  Static Assets  │
│   (Dashboard)   │    │   (Portals)      │    │  (CSS/JS/HTML)  │
└─────────┬───────┘    └─────────┬────────┘    └─────────────────┘
          │                      │
          └──────────┬───────────┘
                     │
          ┌──────────▼───────────┐
          │   Flask Application  │
          │   + SocketIO Server  │
          └──────────┬───────────┘
                     │
          ┌──────────▼───────────┐
          │  Core Components:    │
          │  • RouletteTracker   │
          │  • Session Manager   │
          │  • Audit Logger      │
          │  • Algorithm Engine  │
          └──────────┬───────────┘
                     │
          ┌──────────▼───────────┐
          │   File System:      │
          │  • Session History   │
          │  • Audit Logs       │
          │  • Configuration     │
          └──────────────────────┘
```

### 1.4 File Structure
```
app_1/
├── app.py                      # Main Flask application
├── audit_logger.py             # Custom audit logging module
├── requirements.txt            # Python dependencies
├── templates/                  # Jinja2 HTML templates
│   ├── index.html             # Landing page with code entry
│   ├── admin_login.html       # Admin authentication
│   ├── admin_dashboard.html   # Admin control interface
│   └── user_portal.html       # User number submission interface
├── static/                    # Static web assets
│   ├── css/
│   │   └── style.css         # Complete application styling
│   └── js/
│       └── main.js           # Client-side JavaScript logic
├── logs/                     # Audit and application logs
│   └── audit.log            # Structured audit trail
├── session_history/          # Saved session data
│   ├── session_*.txt        # Individual session files
│   └── session_history_metadata.json  # Session metadata
└── venv/                    # Python virtual environment
```

---

## 2. Feature Specifications

### 2.1 User Management System

#### 2.1.1 Admin Authentication
**Purpose**: Secure access control for administrative functions

**Implementation Details**:
- **Hardcoded Admin User**: Username "scharway", Password "Lookup88?" (hashed with Werkzeug)
- **Session Management**: 32-character hex token-based sessions
- **Session Timeout**: 2 hours of inactivity
- **Rate Limiting**: 5 login attempts per 5-minute window per IP
- **Security Features**: 
  - Password hashing using Werkzeug's `generate_password_hash`
  - CSRF protection via SameSite cookie policy
  - IP-based rate limiting with in-memory store

**User Stories**:
- As an admin, I can log in with username/password to access the dashboard
- As an admin, I am automatically logged out after 2 hours of inactivity
- As an admin, I am protected from brute force attacks via rate limiting

**Acceptance Criteria**:
- Login form validates credentials against hardcoded admin user
- Failed login attempts are logged with IP address and reason
- Successful login creates secure session and redirects to dashboard
- Rate limiting blocks excessive login attempts with 429 status code

#### 2.1.2 User Access Control
**Purpose**: Temporary access for users to submit roulette numbers

**Implementation Details**:
- **One-Time Codes**: 8-character alphanumeric codes (uppercase + digits)
- **Code Validity**: 10 minutes from generation
- **Code Generation Rate Limiting**: 10 codes per minute per IP
- **Special Code**: "admin" redirects to admin login
- **User Identification**: Temporary user IDs based on SocketIO session IDs

**User Stories**:
- As a user, I can enter a valid code to access the number submission portal
- As a user, I can enter "admin" to be redirected to admin login
- As a user, my access expires after the code timeout period

**Acceptance Criteria**:
- Code validation API endpoint returns appropriate success/failure responses
- Expired codes are automatically cleaned up from memory
- Invalid codes return 404 status with appropriate error message
- Valid codes redirect to user portal with admin session context

---

## 3. Data Models

### 3.1 RouletteTracker Class
**Purpose**: Core algorithm engine for tracking roulette spins and calculating predictions

**Data Structure**:
```python
class RouletteTracker:
    def __init__(self, wheel_type='american'):
        self.wheel_type = wheel_type  # 'american' or 'european'
        self.american_wheel_sequence = [38 numbers including '0', '00']
        self.european_wheel_sequence = [37 numbers including '0']
        self.wheel_sequence = self.american_wheel_sequence or self.european_wheel_sequence
        self.num_slots = len(self.wheel_sequence)
        self.spin_distances = deque(range(self.num_slots))  # Distance tracking
        self.last_spin = None  # Last submitted number
        self.history = []  # Complete spin history
```

**Key Methods**:
- `update_spin(number_str)`: Validates and adds new spin to history
- `get_unhit_numbers()`: Returns categorized unhit numbers by distance
- `entry_count()`: Returns total number of spins recorded
- `reset_tracker_and_history()`: Clears all tracking data
- `undo_last_spin()`: Removes most recent spin from history

**Validation Rules**:
- American wheel: Accepts 0-36, '00'
- European wheel: Accepts 0-36 (no '00')
- Input sanitization: Strips whitespace, validates numeric format
- Duplicate consecutive numbers: Allowed
- Invalid numbers: Raises ValueError with descriptive message

### 3.2 Session Data Model
**Purpose**: Manages admin sessions and associated user connections

**Session Structure**:
```python
active_sessions = {
    "session_id": {
        "username": str,                    # Admin username
        "role": "admin",                   # User role
        "last_activity": datetime,         # UTC timestamp
        "wheel_type": str,                 # 'american' or 'european'
        "tracker_instance": RouletteTracker,
        "user_connections": set(),         # SocketIO session IDs
        "user_sids_map": dict,            # SID to temp user ID mapping
        "current_save_file_display_name": str,

        # Statistics tracking
        "spins_since_last_match": int,
        "current_win_streak": int,
        "current_loss_streak": int,
        "max_win_streak": int,
        "max_loss_streak": int,
        "total_wins": int,
        "sum_of_spins_between_wins": int,

        # Algorithm state
        "previous_distill_output_state": {
            "categories": dict,            # Previous distilled categories
            "count": int                   # Total numbers in previous output
        },
        "prev_best_set_for_match_logic": set()
    }
}
```

### 3.3 One-Time Code Model
**Purpose**: Temporary access tokens for user authentication

**Code Structure**:
```python
one_time_codes = {
    "CODE12AB": {
        "admin_session_id": str,           # Associated admin session
        "admin_username": str,             # Admin who generated code
        "created_at": datetime,            # UTC creation timestamp
        "expires_at": datetime             # UTC expiration (created_at + 10 minutes)
    }
}
```

### 3.4 Rate Limiting Model
**Purpose**: Prevent abuse through request rate limiting

**Rate Limit Structure**:
```python
rate_limit_store = {
    "ip_address": {
        "action_type": [last_timestamp, attempt_count]
    }
}
```

**Rate Limit Rules**:
- Login attempts: 5 per 5 minutes per IP
- Code generation: 10 per 1 minute per IP
- Automatic cleanup of expired entries

---

## 4. API Documentation

### 4.1 HTTP Routes

#### 4.1.1 GET /
**Purpose**: Landing page for code entry
**Authentication**: None required
**Response**: HTML template (index.html)
**Template Variables**: None

#### 4.1.2 GET/POST /admin-login
**Purpose**: Admin authentication endpoint

**GET Request**:
- **Authentication**: None required
- **Response**: HTML template (admin_login.html)
- **Redirect Logic**: If already authenticated, redirects to /admin-dashboard

**POST Request**:
- **Content-Type**: application/x-www-form-urlencoded
- **Required Fields**:
  - `username`: Admin username
  - `password`: Admin password
- **Rate Limiting**: 5 attempts per 5 minutes per IP
- **Success Response** (200):
  ```json
  {
    "success": true,
    "session_id": "32-character-hex-string",
    "username": "admin-username",
    "role": "admin"
  }
  ```
- **Error Responses**:
  - 401: Invalid credentials
  - 429: Rate limit exceeded
- **Side Effects**: Creates admin session, sets Flask session cookies

#### 4.1.3 GET /admin-dashboard
**Purpose**: Admin control interface
**Authentication**: Valid admin session required
**Response**: HTML template (admin_dashboard.html)
**Template Variables**:
- `session_id`: Current admin session ID
- `username`: Admin username
**Redirect Logic**: Redirects to /admin-login if not authenticated

#### 4.1.4 GET /user-portal
**Purpose**: User number submission interface
**Authentication**: Valid one-time code required
**Query Parameters**:
- `code`: One-time access code
- `admin_session_id`: Associated admin session
**Response**: HTML template (user_portal.html)
**Template Variables**:
- `one_time_code`: The access code
- `admin_session_id`: Admin session identifier
**Redirect Logic**: Redirects to / if parameters missing

#### 4.1.5 POST /api/validate-code
**Purpose**: Validate one-time access codes
**Content-Type**: application/json
**Request Body**:
```json
{
  "code": "string"
}
```
**Success Response** (200):
```json
{
  "success": true,
  "action": "proceed_to_user_portal",
  "admin_session_id": "session-id",
  "admin_username": "admin-name"
}
```
**Special Case** (admin code):
```json
{
  "success": true,
  "action": "redirect_admin_login"
}
```
**Error Response** (404):
```json
{
  "success": false,
  "message": "Invalid or expired code."
}
```

#### 4.1.6 POST /api/generate-code
**Purpose**: Generate one-time access codes (Admin only)
**Authentication**: Valid admin session required
**Rate Limiting**: 10 codes per minute per IP
**Success Response** (200):
```json
{
  "success": true,
  "code": "ABC12345",
  "expires_in_minutes": 10,
  "admin_username": "admin-name"
}
```
**Error Responses**:
- 401: Unauthorized (invalid session)
- 429: Rate limit exceeded

### 4.2 SocketIO Events

#### 4.2.1 Admin Events

**join_admin_session**
- **Purpose**: Connect admin to their session room
- **Data**: `{"session_id": "admin-session-id"}`
- **Validation**: Session ID must match Flask session
- **Response**: `session_update` event with current state
- **Error Response**: `error_critical` event

**admin_submit_spin**
- **Purpose**: Admin submits roulette numbers
- **Data**: `{"spin_value": "number-string", "session_id": "admin-session-id"}`
- **Validation**: Valid session, valid roulette number format
- **Side Effects**: Updates tracker, calculates new predictions, broadcasts to users
- **Success Response**: `action_feedback` event
- **Error Response**: `error_toast` event

**admin_undo_spin**
- **Purpose**: Remove last submitted number
- **Data**: `{"session_id": "admin-session-id"}`
- **Validation**: Valid session, history not empty
- **Side Effects**: Removes last spin, recalculates predictions
- **Response**: `session_update` event

**admin_reset_session**
- **Purpose**: Clear all tracking data
- **Data**: `{"session_id": "admin-session-id"}`
- **Validation**: Valid admin session
- **Side Effects**: Resets tracker, clears history, updates statistics
- **Response**: `session_update` event

**admin_save_session**
- **Purpose**: Save current session to file
- **Data**: `{"session_id": "admin-session-id", "filename": "optional-filename"}`
- **Validation**: Valid session, non-empty history
- **Side Effects**: Creates file in session_history/, updates metadata
- **Response**: `action_feedback` event with file details

**admin_change_wheel_type**
- **Purpose**: Switch between American/European roulette wheels
- **Data**: `{"wheel_type": "american|european", "session_id": "admin-session-id"}`
- **Validation**: Valid session, valid wheel type
- **Side Effects**: Resets tracker with new wheel, clears history
- **Response**: `session_update` event

#### 4.2.2 User Events

**join_user_to_admin_session**
- **Purpose**: Connect user to admin session
- **Data**: `{"code": "access-code", "admin_session_id": "admin-session-id"}`
- **Validation**: Valid code, active admin session
- **Side Effects**: Adds user to session, generates temp user ID
- **Success Response**: `user_join_success` event
- **Error Response**: `user_join_failed` event
- **Admin Notification**: `user_joined_notification` event

**user_submit_spin**
- **Purpose**: User submits roulette numbers
- **Data**: `{"spin_value": "number-string", "admin_session_id": "admin-session-id"}`
- **Validation**: User authorized in session, valid number format
- **Side Effects**: Same as admin_submit_spin
- **Success Response**: `action_feedback` event
- **Error Response**: `error_toast` event

**user_undo_spin**
- **Purpose**: User requests undo of last number
- **Data**: `{"admin_session_id": "admin-session-id"}`
- **Validation**: User authorized in session
- **Side Effects**: Same as admin_undo_spin
- **Response**: `action_feedback` event

#### 4.2.3 Broadcast Events

**session_update**
- **Purpose**: Real-time state updates to all connected clients
- **Triggered By**: Any data change (new spin, undo, reset, etc.)
- **Data Structure**:
```json
{
  "spinHistory": ["1", "23", "0"],
  "entryCount": 3,
  "statusIndicator": "[RED]",
  "lastSpin": "0",
  "currentDistillOutput": {
    "Zs": ["0", "00"],
    "D1": ["1", "2", "3"],
    "D2": ["4", "5", "6"],
    "D3": ["7", "8", "9"]
  },
  "previousDistillOutput": {...},
  "spinsSinceLastMatch": 5,
  "currentWinStreak": 3,
  "currentLossStreak": 0,
  "maxWinStreak": 8,
  "maxLossStreak": 2,
  "avgSpinsPerWin": 4.2,
  "saveTarget": "session_admin_20250122.txt",
  "connectedUserCount": 2,
  "adminUsername": "admin",
  "currentWheelType": "american",
  "ttsData": {...}
}
```

---

## 5. User Interface Specifications

### 5.1 Landing Page (index.html)
**Purpose**: Entry point for users to access the system

**Layout**:
- Centered card design with responsive layout
- Single input field for access code entry
- Submit button for code validation
- Message area for feedback

**Components**:
- **Code Input Field**:
  - Type: Text input
  - Max length: 8 characters
  - Placeholder: "Enter code here"
  - Autocomplete: Off
  - Required field validation
- **Submit Button**:
  - Text: "Proceed"
  - Full-width primary button
  - Disabled during submission
- **Message Area**:
  - Hidden by default
  - Shows success/error messages
  - Auto-hide after 7 seconds for success messages

**Interactions**:
- Form submission validates code via AJAX
- Special handling for "admin" code (case-insensitive)
- Success redirects to appropriate interface
- Error messages displayed inline

**Responsive Design**:
- Mobile-first approach
- Maximum width: 450px
- Flexible margins and padding
- Touch-friendly button sizes

### 5.2 Admin Login Page (admin_login.html)
**Purpose**: Secure authentication for administrators

**Layout**:
- Centered authentication form
- Username and password fields
- Login button and navigation link

**Components**:
- **Username Field**:
  - Type: Text input
  - Autocomplete: username
  - Required field validation
- **Password Field**:
  - Type: Password input
  - Autocomplete: current-password
  - Required field validation
- **Login Button**:
  - Text: "Login"
  - Full-width primary button
  - Form submission handling
- **Navigation Link**:
  - Text: "User? Enter code here"
  - Links to landing page

**Security Features**:
- Form data submitted via POST
- CSRF protection through Flask sessions
- Rate limiting feedback
- Secure password field

### 5.3 Admin Dashboard (admin_dashboard.html)
**Purpose**: Comprehensive control interface for administrators

**Layout Structure**:
```
┌─────────────────────────────────────────────────┐
│                Header Section                   │
│  Title, Username, Session Info, Logout         │
├─────────────────────────────────────────────────┤
│              Focused View Section               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐│
│  │   Status    │ │  Current    │ │    TTS      ││
│  │   Panel     │ │  Distill    │ │  Controls   ││
│  └─────────────┘ └─────────────┘ └─────────────┘│
│  ┌─────────────────────────────────────────────┐│
│  │           Previous Distill Output           ││
│  └─────────────────────────────────────────────┘│
├─────────────────────────────────────────────────┤
│                Main Grid Section                │
│  ┌─────────────────┐ ┌─────────────────────────┐│
│  │  Left Column    │ │      Right Column       ││
│  │  • Controls     │ │  • Spin History         ││
│  │  • Wheel Config │ │  • Session Management   ││
│  └─────────────────┘ └─────────────────────────┘│
└─────────────────────────────────────────────────┘
```

**Header Components**:
- **Title**: "Roulette Tracker Dashboard"
- **User Info**: Welcome message with username and truncated session ID
- **Connection Count**: Real-time count of connected users
- **Wheel Type**: Current wheel type with status pill styling
- **Logout Button**: Red danger button for session termination

**Status Panel**:
- **Status Indicator**: Color-coded status ([RED], [YELLOW], [GREEN])
- **Entry Count**: Total number of spins recorded
- **Last Spin**: Most recently submitted number
- **Match Statistics**: Spins since last match, win/loss streaks
- **Average Statistics**: Average spins per win
- **Save Target**: Current save file name

**Current Distill Output Panel**:
- **Purpose**: Display current betting recommendations
- **Format**: Preformatted text with categorized numbers
- **Categories**: Zs (zeros), D1, D2, D3 (distance categories)
- **Real-time Updates**: Updates with each new spin

**TTS Controls Panel**:
- **Audio Controls**:
  - Stop button: Halt current speech
  - Mute button: Toggle audio on/off
  - Repeat button: Replay last announcement
  - Speed controls: Slower/Faster buttons with rate display
- **Repetition Settings**:
  - Dropdown: 1-3 repetitions
  - Default: 1 time

**Previous Distill Output Panel**:
- **Purpose**: Show previous prediction for comparison
- **Format**: Same as current distill output
- **Use Case**: Track prediction accuracy

**Control Panel (Left Column)**:
- **Code Generation**:
  - Button: "Generate User Code"
  - Code display area with copy functionality
  - Auto-hide after timeout
- **Number Input**:
  - Text field for spin entry or file base name
  - Placeholder: "e.g., 10 or M-DealerName"
  - Support for multiple formats
- **Action Buttons**:
  - Submit Spin(s): Green success button
  - Undo Last: Yellow warning button
  - Save Session: Blue primary button
  - Reset Session: Red danger button
  - Change File: Secondary button

**Wheel Configuration Panel**:
- **Wheel Type Selector**:
  - Dropdown: American (0, 00) / European (0)
  - Default: American
- **Apply Button**: "Apply & Reset Session"
- **Warning Text**: Explains that changing wheel resets data

**Spin History Panel (Right Column)**:
- **Header**: "Spin History" with count display
- **List Format**: Ordered list of submitted numbers
- **Real-time Updates**: New spins appear at top
- **Empty State**: "No spins yet." message

**Session History Management Panel**:
- **Controls**:
  - Refresh button for file list
  - Dropdown selector for saved sessions
  - View/Delete buttons (enabled when file selected)
- **File Viewer**:
  - Filename display
  - Preformatted content area
  - Default message when no file selected
- **Message Area**: Feedback for file operations

### 5.4 User Portal (user_portal.html)
**Purpose**: Simplified interface for users to submit numbers

**Layout**:
- Clean, focused design
- Minimal distractions
- Mobile-optimized

**Header Section**:
- **Title**: "User Portal"
- **Connection Status**:
  - Visual indicator (green/red/amber)
  - Status text
  - Admin name display
  - Real-time connection monitoring

**Number Submission Section**:
- **Title**: "Submit Your Numbers"
- **Input Field**:
  - Label: "Number(s)"
  - Placeholder: "e.g., 7-15-0"
  - Support for multiple number formats
- **Action Buttons**:
  - Submit Numbers: Green success button
  - Undo Last Entry: Yellow warning button

**Message Area**:
- Real-time feedback for user actions
- Success/error message display
- Auto-hide functionality

**Connection Status Indicators**:
- **Connected**: Green circle, shows admin name
- **Disconnected**: Red circle, "Disconnected" text
- **Connecting**: Amber circle, "Connecting..." text

---

## 6. Business Logic

### 6.1 Core Algorithms

#### 6.1.1 Greedy Hitting Set Algorithm
**Purpose**: Find minimal set of numbers that "hit" (appear in) the maximum number of consecutive number pairs (duplexes) from the spin history.

**Algorithm Implementation**:
```python
def compute_greedy_hitting_set(spins_list_str, randomize=True):
    # Convert spins to consecutive pairs (duplexes)
    spins = [str(s) for s in spins_list_str]
    duplexes = [tuple(spins[i:i+2]) for i in range(len(spins) - 1)]

    # Create mapping of numbers to duplexes they appear in
    spin_to_duplexes = {spin: set() for spin in unique_spins}
    for idx, duplex in enumerate(duplexes):
        for spin_in_duplex in set(duplex):
            spin_to_duplexes[spin_in_duplex].add(idx)

    # Greedy selection: pick number that hits most uncovered duplexes
    hitting_set = set()
    covered_duplexes = set()

    while covered_duplexes != set(range(n_duplexes)):
        # Find number that covers most uncovered duplexes
        best_spin = max(candidates, key=lambda s: len(spin_to_duplexes[s] - covered_duplexes))
        hitting_set.add(best_spin)
        covered_duplexes.update(spin_to_duplexes[best_spin])

    return hitting_set
```

**Optimization**:
- Runs algorithm 1300 times with randomization
- Selects smallest hitting set
- Tie-breaking favors zeros (0, 00) and lexicographic order

#### 6.1.2 Distance-Based Number Tracking
**Purpose**: Track which numbers haven't appeared recently based on wheel position distances.

**Implementation**:
```python
def get_unhit_numbers(self):
    # Calculate distances from last spin position
    last_spin_index = self.wheel_sequence.index(self.last_spin)

    # Categorize by distance ranges
    categories = {'Zs': [], 'D1': [], 'D2': [], 'D3': []}

    for distance in self.spin_distances:
        target_index = (last_spin_index + distance) % self.num_slots
        number = self.wheel_sequence[target_index]

        if number in ['0', '00']:
            categories['Zs'].append(number)
        elif distance <= 3:
            categories['D1'].append(number)
        elif distance <= 6:
            categories['D2'].append(number)
        else:
            categories['D3'].append(number)

    return categories
```

#### 6.1.3 Distilled Output Calculation
**Purpose**: Generate betting recommendations by combining hitting set and distance tracking.

**Algorithm**:
```python
def calculate_distilled_output(tracker_unhit_cat, hitting_set_cat):
    distilled = {'Zs': [], 'D1': [], 'D2': [], 'D3': []}

    # Numbers seen by tracker (recently hit)
    tracker_seen = {num for cat_nums in tracker_unhit_cat.values() for num in cat_nums}

    # For each category, include hitting set numbers not recently seen
    for cat_key, hs_nums in hitting_set_cat.items():
        dist_cat_nums = [num for num in hs_nums if num not in tracker_seen]
        distilled[cat_key] = dist_cat_nums

    return distilled, total_count
```

**Output Format**:
```
Zs: 0, 00
D1: 1, 13, 36
D2: 24, 3, 15
D3: 34, 22, 5
```

#### 6.1.4 Win/Loss Streak Tracking
**Purpose**: Track prediction accuracy and betting performance statistics.

**Logic**:
- **Win**: Submitted number does NOT appear in previous distilled output
- **Loss**: Submitted number DOES appear in previous distilled output
- **Rationale**: Distilled output represents least likely numbers to hit

**Statistics Maintained**:
- Current win/loss streaks
- Maximum win/loss streaks
- Total wins
- Average spins between wins
- Spins since last match

### 6.2 Number Validation Rules

#### 6.2.1 Input Format Support
**Supported Formats**:
- Single numbers: "17", "0", "00"
- Multiple numbers: "17-23-0", "17,23,0", "17 23 0"
- File base names: "M-DealerName", "E-Table1"

**Validation Process**:
1. Trim whitespace
2. Check for file base pattern (letter-dash-text)
3. Split on delimiters (dash, comma, space)
4. Validate each number against wheel type
5. Convert to string format for storage

#### 6.2.2 Wheel-Specific Validation
**American Wheel** (38 numbers):
- Valid: 0-36, "00"
- Invalid: Any other input

**European Wheel** (37 numbers):
- Valid: 0-36
- Invalid: "00", any other input

**Error Handling**:
- Invalid numbers raise ValueError with descriptive message
- Empty input rejected
- Non-numeric input (except "00") rejected

### 6.3 Session Management Logic

#### 6.3.1 Session Lifecycle
1. **Creation**: Admin login creates session with unique ID
2. **Activity Tracking**: Last activity updated on each request
3. **Timeout**: Sessions expire after 2 hours of inactivity
4. **Cleanup**: Expired sessions automatically removed

#### 6.3.2 User Connection Management
- Users join admin sessions via one-time codes
- Each user gets temporary ID based on SocketIO session
- User disconnections automatically clean up references
- Admin can see real-time count of connected users

#### 6.3.3 Code Management
- Codes expire after 10 minutes
- Expired codes automatically cleaned up
- Each code tied to specific admin session
- Rate limiting prevents code spam

---

## 7. Security Requirements

### 7.1 Authentication Security
**Password Security**:
- Werkzeug PBKDF2 password hashing
- Salt-based hashing prevents rainbow table attacks
- Hardcoded admin credentials (production warning included)

**Session Security**:
- 32-character cryptographically secure session tokens
- SameSite cookie policy prevents CSRF attacks
- Session timeout prevents indefinite access
- Secure session storage in server memory

### 7.2 Rate Limiting
**Login Protection**:
- 5 failed attempts per 5 minutes per IP address
- Automatic lockout with 429 status code
- Rate limit data stored in memory with automatic cleanup

**Code Generation Protection**:
- 10 code generations per minute per IP address
- Prevents code enumeration attacks
- Protects against resource exhaustion

### 7.3 Input Validation
**Number Input Sanitization**:
- Strict validation against wheel number sets
- Regex-based input cleaning
- SQL injection prevention (though no SQL database used)
- XSS prevention through proper escaping

**File Name Sanitization**:
- Regex cleaning of session file names
- Path traversal prevention
- Safe character set enforcement

### 7.4 Audit Logging
**Comprehensive Event Logging**:
- All authentication attempts (success/failure)
- User actions (number submissions, undos)
- Admin actions (session management, code generation)
- Security events (rate limiting, invalid access)

**Log Format**:
```json
{
  "timestamp": "2025-01-22T10:30:45.123456Z",
  "event_type": "sec.login",
  "user_id": "admin",
  "ip_address": "*************",
  "status": "ok",
  "request_details": {
    "path": "/admin-login",
    "method": "POST",
    "user_agent": "Mozilla/5.0..."
  },
  "additional_details": {
    "s_id": "session-id-here"
  }
}
```

**Log Management**:
- Rotating file handler (10MB per file, 5 backups)
- UTF-8 encoding
- Structured JSON format for parsing
- Automatic log rotation prevents disk space issues

---

## 8. Performance Requirements

### 8.1 Response Time Targets
**Web Page Load Times**:
- Landing page: < 1 second
- Admin dashboard: < 2 seconds
- User portal: < 1 second
- Admin login: < 1 second

**API Response Times**:
- Code validation: < 500ms
- Number submission: < 200ms
- Session updates: < 100ms
- File operations: < 1 second

**Real-time Communication**:
- SocketIO connection: < 1 second
- Event propagation: < 50ms
- Broadcast updates: < 100ms

### 8.2 Scalability Targets
**Concurrent Users**:
- Maximum admin sessions: 10 concurrent
- Users per admin session: 50 concurrent
- Total system capacity: 500 concurrent connections

**Memory Usage**:
- Base application: < 50MB
- Per admin session: < 5MB
- Per user connection: < 1MB
- Session history: Configurable limit (default 100 files)

**Algorithm Performance**:
- Hitting set calculation: < 2 seconds for 1000+ spins
- Distilled output: < 100ms
- Number validation: < 10ms
- Statistics calculation: < 50ms

### 8.3 Caching Strategy
**Static Asset Caching**:
- CSS/JS files: Browser cache with versioning
- HTML templates: Server-side template caching
- Font files: Long-term browser caching

**Data Caching**:
- Session data: In-memory storage
- Algorithm results: Cached until next spin
- Rate limit data: In-memory with TTL cleanup

---

## 9. Error Handling

### 9.1 Client-Side Error Handling
**Form Validation Errors**:
- Empty required fields: Inline validation messages
- Invalid code format: Real-time feedback
- Network errors: User-friendly error messages
- Timeout errors: Automatic retry suggestions

**SocketIO Connection Errors**:
- Connection lost: Visual indicator and reconnection attempts
- Authentication failures: Redirect to appropriate login
- Permission errors: Clear error messages
- Server unavailable: Graceful degradation

**JavaScript Error Handling**:
- Try-catch blocks around critical operations
- Console logging for debugging
- Fallback behavior for failed operations
- User notification for critical failures

### 9.2 Server-Side Error Handling
**HTTP Error Responses**:
- 400 Bad Request: Invalid input data
- 401 Unauthorized: Authentication required
- 403 Forbidden: Insufficient permissions
- 404 Not Found: Invalid codes or resources
- 429 Too Many Requests: Rate limit exceeded
- 500 Internal Server Error: Unexpected server errors

**SocketIO Error Handling**:
- Invalid session: `error_critical` event with redirect
- Permission denied: `error_toast` event with message
- Validation failures: `error_toast` event with details
- Server errors: Graceful error responses

**Algorithm Error Handling**:
- Invalid number input: ValueError with descriptive message
- Empty history: Graceful handling with default values
- Calculation failures: Fallback to previous state
- File operation errors: User notification and logging

### 9.3 Logging and Monitoring
**Error Logging**:
- All exceptions logged with stack traces
- User actions logged for debugging
- Performance metrics tracked
- Security events highlighted

**Error Recovery**:
- Automatic session cleanup on errors
- Graceful degradation of features
- User notification of temporary issues
- Administrative alerts for critical errors

---

## 10. Testing Strategy

### 10.1 Unit Testing Requirements
**Core Algorithm Testing**:
- RouletteTracker class methods
- Hitting set algorithm correctness
- Distance calculation accuracy
- Number validation logic
- Statistics calculation

**Utility Function Testing**:
- Session management functions
- Rate limiting logic
- Code generation and validation
- File operations
- Input sanitization

**Test Coverage Targets**:
- Core algorithms: 100% coverage
- API endpoints: 95% coverage
- Utility functions: 90% coverage
- Error handling: 85% coverage

### 10.2 Integration Testing
**API Endpoint Testing**:
- Authentication flow testing
- Code validation workflow
- Session management lifecycle
- File upload/download operations
- Rate limiting enforcement

**SocketIO Event Testing**:
- Connection establishment
- Event broadcasting
- Room management
- Error propagation
- Disconnection handling

**Database Integration**:
- File system operations
- Session persistence
- Audit log writing
- Metadata management

### 10.3 User Acceptance Testing
**Admin Workflow Testing**:
- Complete session lifecycle
- Number submission and tracking
- Code generation and management
- File operations
- Wheel type switching

**User Workflow Testing**:
- Code entry and validation
- Number submission
- Real-time updates
- Error handling
- Connection management

**Cross-Browser Testing**:
- Chrome, Firefox, Safari, Edge
- Mobile browsers (iOS Safari, Chrome Mobile)
- WebSocket compatibility
- Responsive design validation

### 10.4 Performance Testing
**Load Testing**:
- Concurrent user simulation
- Algorithm performance under load
- Memory usage monitoring
- Response time measurement

**Stress Testing**:
- Maximum connection limits
- Resource exhaustion scenarios
- Recovery from failures
- Rate limiting effectiveness

---

## 11. Deployment Specifications

### 11.1 Environment Requirements
**Python Environment**:
- Python 3.11+ required
- Virtual environment recommended
- pip package manager

**System Requirements**:
- Linux/Unix-based system preferred
- Minimum 2GB RAM
- 10GB disk space for logs and sessions
- Network connectivity for WebSocket support

**Dependencies**:
```
Flask>=2.0
Flask-SocketIO>=5.0
Werkzeug>=2.0
python-dotenv
eventlet
```

### 11.2 Configuration Management
**Environment Variables**:
- `SECRET_KEY`: Flask secret key (auto-generated if not set)
- `DEBUG`: Debug mode flag (default: False)
- `HOST`: Server host (default: 127.0.0.1)
- `PORT`: Server port (default: 5000)

**Configuration Constants**:
```python
LOGS_DIRECTORY = "logs"
SESSION_HISTORY_DIRECTORY = "session_history"
RED_THRESHOLD = 70
YELLOW_THRESHOLD = 90
SESSION_INACTIVITY_TIMEOUT = timedelta(hours=2)
ONE_TIME_CODE_VALIDITY = timedelta(minutes=10)
LOGIN_ATTEMPT_LIMIT = 5
LOGIN_ATTEMPT_WINDOW = timedelta(minutes=5)
```

### 11.3 Deployment Process
**Installation Steps**:
1. Clone/extract application files
2. Create Python virtual environment
3. Install dependencies from requirements.txt
4. Create necessary directories (logs, session_history)
5. Configure environment variables
6. Start application with `python app.py`

**Production Considerations**:
- Change default admin password
- Configure proper logging levels
- Set up log rotation
- Configure firewall rules
- Enable HTTPS (reverse proxy recommended)
- Set up monitoring and alerting

**Service Configuration**:
- Systemd service file provided (app_1.service)
- Automatic restart on failure
- Proper user permissions
- Log file management

### 11.4 Monitoring and Maintenance
**Health Monitoring**:
- Application uptime monitoring
- Memory usage tracking
- Disk space monitoring
- Log file size monitoring

**Maintenance Tasks**:
- Regular log file cleanup
- Session history archival
- Security updates
- Performance optimization
- Backup procedures

---

## 12. Conclusion

This Product Requirements Document provides a comprehensive specification for the Real-Time Roulette Tracking and Analytics Application. The system combines sophisticated mathematical algorithms with real-time web technologies to provide an advanced roulette analysis platform.

### Key Features Summary:
- **Real-time Analytics**: Advanced hitting set algorithms for number prediction
- **Multi-user Support**: Admin/user role system with secure access control
- **WebSocket Communication**: Real-time updates and collaboration
- **Comprehensive Logging**: Full audit trail for security and debugging
- **Mobile-Responsive Design**: Works across all device types
- **Session Management**: Persistent session history and file management
- **Text-to-Speech Integration**: Audio feedback for predictions
- **Security Features**: Rate limiting, input validation, secure sessions

### Technical Excellence:
- **Scalable Architecture**: Supports multiple concurrent sessions
- **Performance Optimized**: Sub-second response times for critical operations
- **Error Resilient**: Comprehensive error handling and recovery
- **Maintainable Code**: Well-structured, documented, and testable
- **Production Ready**: Complete deployment and monitoring specifications

This specification serves as a complete blueprint for recreating or extending the application, ensuring all functionality, security requirements, and performance characteristics are preserved and documented.
